package handlers

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/finework/finework-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewPMOProjectHTTP(e *echo.Echo) {
	pmoProject := &PMOProjectController{}

	// PMO Project routes
	e.GET("/pmo/projects", core.WithHTTPContext(pmoProject.Pagination), middleware.AuthMiddleware())
	e.GET("/pmo/projects/:id", core.WithHTTPContext(pmoProject.Find), middleware.AuthMiddleware())
	e.POST("/pmo/projects/check-slug", core.WithHTTPContext(pmoProject.CheckSlug), middleware.AuthMiddleware())
	e.POST("/pmo/projects", core.WithHTTPContext(pmoProject.Create), middleware.AuthMiddleware())
	e.PUT("/pmo/projects/:id", core.WithHTTPContext(pmoProject.Update), middleware.AuthMiddleware())
	e.DELETE("/pmo/projects/:id", core.WithHTTPContext(pmoProject.Delete), middleware.AuthMiddleware())

	pmoProjectComment := &PMOProjectCommentController{}

	e.GET("/pmo/projects/:id/comments", core.WithHTTPContext(pmoProjectComment.CommentsPagination), middleware.AuthMiddleware())
	e.GET("/pmo/projects/:id/comments/:comment_id", core.WithHTTPContext(pmoProjectComment.CommentsFind), middleware.AuthMiddleware())
	e.POST("/pmo/projects/:id/comments", core.WithHTTPContext(pmoProjectComment.CommentsCreate), middleware.AuthMiddleware())
	e.PUT("/pmo/projects/:id/comments/:comment_id", core.WithHTTPContext(pmoProjectComment.CommentsUpdate), middleware.AuthMiddleware())
	e.DELETE("/pmo/projects/:id/comments/:comment_id", core.WithHTTPContext(pmoProjectComment.CommentsDelete), middleware.AuthMiddleware())
	e.GET("/pmo/projects/:id/comments/:comment_id/versions", core.WithHTTPContext(pmoProjectComment.CommentsVersionsPagination), middleware.AuthMiddleware())
	e.GET("/pmo/projects/:id/comments/:comment_id/versions/:version_id", core.WithHTTPContext(pmoProjectComment.CommentsVersionsFind), middleware.AuthMiddleware())

	pmoProjectCollaborator := &PMOProjectCollaboratorController{}
	e.GET("/pmo/projects/:id/collaborators", core.WithHTTPContext(pmoProjectCollaborator.CollaboratorsPagination), middleware.AuthMiddleware())
	e.GET("/pmo/projects/:id/collaborators/:collaborator_id", core.WithHTTPContext(pmoProjectCollaborator.CollaboratorsFind), middleware.AuthMiddleware())
	e.POST("/pmo/projects/:id/collaborators", core.WithHTTPContext(pmoProjectCollaborator.CollaboratorsCreate), middleware.AuthMiddleware())
	e.PUT("/pmo/projects/:id/collaborators/:collaborator_id", core.WithHTTPContext(pmoProjectCollaborator.CollaboratorsUpdate), middleware.AuthMiddleware())
	e.DELETE("/pmo/projects/:id/collaborators/:collaborator_id", core.WithHTTPContext(pmoProjectCollaborator.CollaboratorsDelete), middleware.AuthMiddleware())

	pmoProjectDocument := &PMOProjectDocumentController{}
	e.GET("/pmo/projects/:id/documents/groups", core.WithHTTPContext(pmoProjectDocument.DocumentGroupsPagination), middleware.AuthMiddleware())
	e.GET("/pmo/projects/:id/documents/groups/:group_id", core.WithHTTPContext(pmoProjectDocument.DocumentGroupsFind), middleware.AuthMiddleware())
	e.POST("/pmo/projects/:id/documents/groups", core.WithHTTPContext(pmoProjectDocument.DocumentGroupsCreate), middleware.AuthMiddleware())
	e.PUT("/pmo/projects/:id/documents/groups/:group_id", core.WithHTTPContext(pmoProjectDocument.DocumentGroupsUpdate), middleware.AuthMiddleware())
	e.DELETE("/pmo/projects/:id/documents/groups/:group_id", core.WithHTTPContext(pmoProjectDocument.DocumentGroupsDelete), middleware.AuthMiddleware())

	e.GET("/pmo/projects/:id/documents/items", core.WithHTTPContext(pmoProjectDocument.DocumentItemsPagination), middleware.AuthMiddleware())
	e.GET("/pmo/projects/:id/documents/items/:item_id", core.WithHTTPContext(pmoProjectDocument.DocumentItemsFind), middleware.AuthMiddleware())
	e.POST("/pmo/projects/:id/documents/items", core.WithHTTPContext(pmoProjectDocument.DocumentItemsCreate), middleware.AuthMiddleware())
	e.PUT("/pmo/projects/:id/documents/items/:item_id", core.WithHTTPContext(pmoProjectDocument.DocumentItemsUpdate), middleware.AuthMiddleware())
	e.DELETE("/pmo/projects/:id/documents/items/:item_id", core.WithHTTPContext(pmoProjectDocument.DocumentItemsDelete), middleware.AuthMiddleware())

	e.GET("/pmo/projects/:id/documents/items/:item_id/versions", core.WithHTTPContext(pmoProjectDocument.DocumentItemVersionsPagination), middleware.AuthMiddleware())
	e.GET("/pmo/projects/:id/documents/items/:item_id/versions/:version_id", core.WithHTTPContext(pmoProjectDocument.DocumentItemVersionsFind), middleware.AuthMiddleware())

	pmoProjectRemark := &PMOProjectRemarkController{}
	e.GET("/pmo/projects/:id/remark", core.WithHTTPContext(pmoProjectRemark.RemarkFind), middleware.AuthMiddleware())
	e.POST("/pmo/projects/:id/remark", core.WithHTTPContext(pmoProjectRemark.RemarksCreate), middleware.AuthMiddleware())
	e.GET("/pmo/projects/:id/remark/versions", core.WithHTTPContext(pmoProjectRemark.RemarksVersions), middleware.AuthMiddleware())

	pmoProjectContact := &PMOProjectContactController{}
	e.GET("/pmo/projects/:id/contacts", core.WithHTTPContext(pmoProjectContact.ContactsPagination), middleware.AuthMiddleware())
	e.GET("/pmo/projects/:id/contacts/:contact_id", core.WithHTTPContext(pmoProjectContact.ContactsFind), middleware.AuthMiddleware())
	e.POST("/pmo/projects/:id/contacts", core.WithHTTPContext(pmoProjectContact.ContactsCreate), middleware.AuthMiddleware())
	e.PUT("/pmo/projects/:id/contacts/:contact_id", core.WithHTTPContext(pmoProjectContact.ContactsUpdate), middleware.AuthMiddleware())
	e.DELETE("/pmo/projects/:id/contacts/:contact_id", core.WithHTTPContext(pmoProjectContact.ContactsDelete), middleware.AuthMiddleware())

	pmoProjectCompetitor := &PMOProjectCompetitorController{}
	e.GET("/pmo/projects/:id/competitors", core.WithHTTPContext(pmoProjectCompetitor.CompetitorsPagination), middleware.AuthMiddleware())
	e.GET("/pmo/projects/:id/competitors/:competitor_id", core.WithHTTPContext(pmoProjectCompetitor.CompetitorsFind), middleware.AuthMiddleware())
	e.POST("/pmo/projects/:id/competitors", core.WithHTTPContext(pmoProjectCompetitor.CompetitorsCreate), middleware.AuthMiddleware())
	e.PUT("/pmo/projects/:id/competitors/:competitor_id", core.WithHTTPContext(pmoProjectCompetitor.CompetitorsUpdate), middleware.AuthMiddleware())
	e.DELETE("/pmo/projects/:id/competitors/:competitor_id", core.WithHTTPContext(pmoProjectCompetitor.CompetitorsDelete), middleware.AuthMiddleware())

	pmoProjectPartner := &PMOProjectPartnerController{}
	e.GET("/pmo/projects/:id/partners", core.WithHTTPContext(pmoProjectPartner.PartnersPagination), middleware.AuthMiddleware())
	e.GET("/pmo/projects/:id/partners/:partner_id", core.WithHTTPContext(pmoProjectPartner.PartnersFind), middleware.AuthMiddleware())
	e.POST("/pmo/projects/:id/partners", core.WithHTTPContext(pmoProjectPartner.PartnersCreate), middleware.AuthMiddleware())
	e.PUT("/pmo/projects/:id/partners/:partner_id", core.WithHTTPContext(pmoProjectPartner.PartnersUpdate), middleware.AuthMiddleware())
	e.DELETE("/pmo/projects/:id/partners/:partner_id", core.WithHTTPContext(pmoProjectPartner.PartnersDelete), middleware.AuthMiddleware())

	pmoProjectBudgetInfo := &PMOProjectBudgetController{}
	e.GET("/pmo/projects/:id/budget", core.WithHTTPContext(pmoProjectBudgetInfo.BudgetFind), middleware.AuthMiddleware())
	e.POST("/pmo/projects/:id/budget", core.WithHTTPContext(pmoProjectBudgetInfo.BudgetCreate), middleware.AuthMiddleware())
	e.GET("/pmo/projects/:id/budget/versions", core.WithHTTPContext(pmoProjectBudgetInfo.BudgetVersionsFind), middleware.AuthMiddleware())

	pmoProjectBiddingInfo := &PMOProjectBiddingController{}
	e.GET("/pmo/projects/:id/bidding", core.WithHTTPContext(pmoProjectBiddingInfo.BiddingFind), middleware.AuthMiddleware())
	e.POST("/pmo/projects/:id/bidding", core.WithHTTPContext(pmoProjectBiddingInfo.BiddingCreate), middleware.AuthMiddleware())
	e.GET("/pmo/projects/:id/bidding/versions", core.WithHTTPContext(pmoProjectBiddingInfo.BiddingVersionsFind), middleware.AuthMiddleware())

	pmoProjectContractInfo := &PMOProjectContractController{}
	e.GET("/pmo/projects/:id/contract", core.WithHTTPContext(pmoProjectContractInfo.ContractFind), middleware.AuthMiddleware())
	e.POST("/pmo/projects/:id/contract", core.WithHTTPContext(pmoProjectContractInfo.ContractCreate), middleware.AuthMiddleware())
	e.GET("/pmo/projects/:id/contract/versions", core.WithHTTPContext(pmoProjectContractInfo.ContractVersionsFind), middleware.AuthMiddleware())

	pmoProjectBidbondInfo := &PMOProjectBidbondController{}
	e.GET("/pmo/projects/:id/bidbond", core.WithHTTPContext(pmoProjectBidbondInfo.BidbondFind), middleware.AuthMiddleware())
	e.POST("/pmo/projects/:id/bidbond", core.WithHTTPContext(pmoProjectBidbondInfo.BidbondCreate), middleware.AuthMiddleware())
	e.GET("/pmo/projects/:id/bidbond/versions", core.WithHTTPContext(pmoProjectBidbondInfo.BidbondVersionsFind), middleware.AuthMiddleware())
}
